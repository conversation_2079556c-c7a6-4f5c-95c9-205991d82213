# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
out/
*.tsbuildinfo

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Vite build outputs
dist-ssr
*.local

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Storybook build outputs
storybook-static

# Tailwind CSS
.tailwindcss

# Build tools
.turbo

# Sentry
.sentryclirc

# Local Netlify folder
.netlify

# Vercel
.vercel

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Supabase
.supabase/

# Testing
coverage/
.nyc_output/
junit.xml

# Playwright
test-results/
playwright-report/
playwright/.cache/

# MSW
public/mockServiceWorker.js

# Local database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Certificate files
*.pem
*.key
*.crt
*.cert

# Local configuration
config.local.js
config.local.ts
config.local.json
