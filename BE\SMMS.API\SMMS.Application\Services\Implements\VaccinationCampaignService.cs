﻿

using Microsoft.EntityFrameworkCore;
using SMMS.Application.DataObject.RequestObject;
using SMMS.Application.DataObject.ResponseObject;
using SMMS.Application.Services.Interfaces;
using SMMS.Domain.Entity;
using SMMS.Domain.Enum;
using SMMS.Domain.Interface.Repositories;

namespace SMMS.Application.Services.Implements
{
	public class VaccinationCampaignService : IVaccinationCampaignService
	{
		private readonly IRepositoryManager _repositoryManager;
		private readonly INotificationService _notificationService;

		public VaccinationCampaignService(IRepositoryManager repositoryManager, INotificationService notificationService)
		{
			_repositoryManager = repositoryManager;
			_notificationService = notificationService;
		}

		public async Task<VaccinationCampaignResponse> CreateVaccinationCampaignAsync(VaccinationCampaignRequest request, string nurseId)
		{
			// Validate dates
			if (request.StartDate >= request.EndDate)
			{
				throw new Exception("<PERSON><PERSON><PERSON> bắt đầu phải trước ngày kết thúc.");
			}

			if (request.MFG >= request.EXP)
			{
				throw new Exception("Ngày sản xuất phải trước ngày hết hạn.");
			}

			var currentDate = DateTime.Now.Date;
			if (request.MFG.Date > currentDate)
			{
				throw new Exception("Ngày sản xuất không thể trong tương lai.");
			}

			if (request.EXP.Date <= currentDate)
			{
				throw new Exception("Ngày hết hạn phải trong tương lai.");
			}

			if (request.StartDate.Date < currentDate)
			{
				throw new Exception("Ngày bắt đầu không thể trong quá khứ.");
			}

			var existingClassIds = _repositoryManager.ClassRepository
			.FindByCondition(c => request.ClassIds.Contains(c.Id) && c.DeletedTime == null, false)
			.Select(c => c.Id)
			.ToList();
			if (existingClassIds.Count != request.ClassIds.Count)
			{
				throw new Exception("Một hoặc nhiều lớp không tồn tại.");
			}
			var campaign = new VaccinationCampaign
			{
				UserId = nurseId,
				Name = request.Name ?? string.Empty,
				VaccineName = request.VaccineName ?? string.Empty,
				EXP = request.EXP,
				MFG = request.MFG,
				VaccineType = request.VaccineType ?? string.Empty,
				StartDate = request.StartDate,
				EndDate = request.EndDate,
				Status = ApprovalStatus.Pending,
				CreatedBy = nurseId,
				CreatedTime = DateTimeOffset.UtcNow,
				VaccinationCampaignClasses = request.ClassIds.Select(classId => new VaccinationCampaignClass
				{
					SchoolClassId = classId ?? string.Empty,
					CreatedBy = nurseId,
					CreatedTime = DateTimeOffset.UtcNow
				}).ToList()
			};
			_repositoryManager.VaccinationCampaignRepository.Create(campaign);
			await _repositoryManager.SaveAsync();

			// Notify Admins///////////////////////////////////////////
			var admins = await _repositoryManager.UserRepository
				.FindByCondition(u => u.Role.RoleName == "Admin" && u.DeletedTime == null, false)
				.ToListAsync();
			foreach (var admin in admins)
			{
				await _notificationService.CreateNotificationAsync(
					admin.Id,
					"Chiến dịch tiêm chủng mới cần xét duyệt",
					$"Chiến dịch tiêm chủng: {campaign.Name} cần được xét duyệt ngay bây giờ."
					, campaign.Id
				);
			}

			return new VaccinationCampaignResponse
			{
				Id = campaign.Id,
				Name = campaign.Name,
				VaccineName = campaign.VaccineName,
				NurseId = campaign.UserId,
				NurseName = _repositoryManager.UserRepository
					.FindByCondition(u => u.Id == campaign.UserId, false)
					.Select(u => u.FullName)
					.FirstOrDefault(),
				EXP = campaign.EXP,
				MFG = campaign.MFG,
				VaccineType = campaign.VaccineType,
				StartDate = campaign.StartDate,
				EndDate = campaign.EndDate,
				Status = campaign.Status,
				ClassIds = campaign.VaccinationCampaignClasses.Select(vcc => vcc.SchoolClassId).ToList()
			};
		}

		public async Task<bool> UpdateVaccineCampaignStatusAsync(string vcId, string action, string userId)
		{
			var vaccination = _repositoryManager.VaccinationCampaignRepository
				.FindByCondition(vc => vc.Id == vcId && vc.Status == ApprovalStatus.Pending && vc.DeletedTime == null, true)
				.Include(vc => vc.VaccinationCampaignClasses)
				.FirstOrDefault();
			if (vaccination == null) return false;

			if (action == "approve")
			{
				vaccination.Status = ApprovalStatus.Approved;
				await CreateActivityConsentsAsync(vaccination);

				// Notify Nurse///////////////////////
				await _notificationService.CreateNotificationAsync(
					vaccination.UserId,
					"Chiến dịch tiêm chủng đã được chấp nhận",
					$"Chiến dịch: {vaccination.Name} đã được duyệt."
					, vaccination.Id
				);
			}
			else if (action == "reject")
			{
				vaccination.Status = ApprovalStatus.Rejected;
				await _notificationService.CreateNotificationAsync(
					vaccination.UserId,
					"Chiến dịch tiêm chủng đã bị từ chối",
					$"Chiến dịch: {vaccination.Name} đã từ chối."
					, vaccination.Id
				);
			}
			else
			{
				return false;
			}

			vaccination.LastUpdatedBy = userId;
			vaccination.LastUpdatedTime = DateTimeOffset.UtcNow;
			_repositoryManager.VaccinationCampaignRepository.Update(vaccination);
			await _repositoryManager.SaveAsync();
			return true;
		}

		private async Task CreateActivityConsentsAsync(VaccinationCampaign campaign)
		{
			var classIds = campaign.VaccinationCampaignClasses.Where(vcc => vcc.DeletedTime == null).Select(vcc => vcc.SchoolClassId).ToList();
			var students = await Task.Run(() => _repositoryManager.StudentRepository
				.FindByCondition(s => classIds.Contains(s.ClassId) && s.DeletedTime == null, false)
				.ToList());

			foreach (var student in students)
			{
				var consent = new ActivityConsent
				{
					StudentId = student.Id,
					UserId = student.ParentId,
					ScheduleTime = campaign.StartDate,
					VaccinationCampaignId = campaign.Id,
					HealthActivityId = null,
					Status = ApprovalStatus.Pending,
					Comments = campaign.VaccineName,
					CreatedBy = "System",
					CreatedTime = DateTimeOffset.UtcNow,
					ActivityType = "VaccinationCampaign"
				};
				_repositoryManager.ConsentRepository.Create(consent);
				await _repositoryManager.SaveAsync();

				// Create notification for each consent/////////////////////////////////////
				await _notificationService.CreateNotificationAsync(
						student.ParentId,
						"Chiến dịch tiêm chủng mới cho con của bạn",
						$"Chiến dịch: {campaign.Name}. Hãy xác nhận chiến dịch."
						, campaign.Id
					);
			}
		}

		public async Task<List<VaccinationCampaignResponse>> GetAllVaccineCampaignAsync()
		{
			return await Task.Run(() => _repositoryManager.VaccinationCampaignRepository
				.FindAll(false)
				.Include(vc => vc.VaccinationCampaignClasses)
				.Include(vc => vc.User)
				.Select(vc => new VaccinationCampaignResponse
				{
					Id = vc.Id,
					Name = vc.Name,
					VaccineName = vc.VaccineName,
					NurseId = vc.UserId,
					NurseName = vc.User.FullName,
					EXP = vc.EXP,
					MFG = vc.MFG,
					VaccineType = vc.VaccineType,
					StartDate = vc.StartDate,
					EndDate = vc.EndDate,
					Status = vc.Status,
					ClassIds = vc.VaccinationCampaignClasses.Where(vcc => vcc.DeletedTime == null).Select(vcc => vcc.SchoolClassId).ToList()
				}).ToList());
		}

		public async Task<List<VaccinationCampaignResponse>> GetPendingVaccinationCampaignsAsync()
		{
			return await Task.Run(() => _repositoryManager.VaccinationCampaignRepository
				.FindByCondition(vc => vc.Status == ApprovalStatus.Pending, false)
				.Include(vc => vc.VaccinationCampaignClasses)
				.Include(vc => vc.User)
				.Select(vc => new VaccinationCampaignResponse
				{
					Id = vc.Id,
					Name = vc.Name,
					VaccineName = vc.VaccineName,
					NurseId = vc.UserId,
					NurseName = vc.User.FullName,
					EXP = vc.EXP,
					MFG = vc.MFG,
					VaccineType = vc.VaccineType,
					StartDate = vc.StartDate,
					EndDate = vc.EndDate,
					Status = vc.Status,
					ClassIds = vc.VaccinationCampaignClasses.Where(vcc => vcc.DeletedTime == null).Select(vcc => vcc.SchoolClassId).ToList()
				}).ToList());
		}

		public async Task<List<VaccinationCampaignResponse>> GetCampaignWithoutPendingAsync()
		{
			return await Task.Run(() => _repositoryManager.VaccinationCampaignRepository
				.FindByCondition(vc => vc.Status == ApprovalStatus.Approved || vc.Status == ApprovalStatus.Rejected, false)
				.Include(vc => vc.VaccinationCampaignClasses)
				.Include(vc => vc.User)
				.Select(vc => new VaccinationCampaignResponse
				{
					Id = vc.Id,
					Name = vc.Name,
					VaccineName = vc.VaccineName,
					NurseId = vc.UserId,
					NurseName = vc.User.FullName,
					EXP = vc.EXP,
					MFG = vc.MFG,
					VaccineType = vc.VaccineType,
					StartDate = vc.StartDate,
					EndDate = vc.EndDate,
					Status = vc.Status,
					ClassIds = vc.VaccinationCampaignClasses.Where(vcc => vcc.DeletedTime == null).Select(vcc => vcc.SchoolClassId).ToList()
				}).ToList());
		}

		public async Task<bool> UpdateVaccinationCampaignAsync(string vaccinationCampaignId, VaccinationCampaignRequest request, string userId)
		{
			var campaign = _repositoryManager.VaccinationCampaignRepository
				.FindByCondition(vc => vc.Id == vaccinationCampaignId && vc.Status == ApprovalStatus.Pending, true)
				.Include(vc => vc.VaccinationCampaignClasses)
				.FirstOrDefault();
			if (campaign == null) return false;

			var user = _repositoryManager.UserRepository
				.FindByCondition(u => u.Id == userId, false)
				.Include(u => u.Role)
				.FirstOrDefault();
			if (campaign.CreatedBy != userId && (user.Role?.RoleName != "Admin" && user.Role?.RoleName != "Manager"))
			{
				return false;
			}

			// Validate dates
			if (request.StartDate >= request.EndDate)
			{
				throw new Exception("Ngày bắt đầu phải trước ngày kết thúc.");
			}

			if (request.MFG >= request.EXP)
			{
				throw new Exception("Ngày sản xuất phải trước ngày hết hạn.");
			}

			var currentDate = DateTime.Now.Date;
			if (request.MFG.Date > currentDate)
			{
				throw new Exception("Ngày sản xuất không thể trong tương lai.");
			}

			if (request.EXP.Date <= currentDate)
			{
				throw new Exception("Ngày hết hạn phải trong tương lai.");
			}

			if (request.StartDate.Date < currentDate)
			{
				throw new Exception("Ngày bắt đầu không thể trong quá khứ.");
			}

			// Validate that all requested class IDs exist
			var existingClassIds = _repositoryManager.ClassRepository
				.FindByCondition(c => request.ClassIds.Contains(c.Id) && c.DeletedTime == null, false)
				.Select(c => c.Id)
				.ToList();
			if (existingClassIds.Count != request.ClassIds.Count)
			{
				throw new Exception("Một hoặc nhiều lớp không tồn tại.");
			}

			// Update campaign properties
			campaign.Name = request.Name ?? string.Empty;
			campaign.VaccineName = request.VaccineName ?? string.Empty;
			campaign.EXP = request.EXP;
			campaign.MFG = request.MFG;
			campaign.VaccineType = request.VaccineType ?? string.Empty;
			campaign.StartDate = request.StartDate;
			campaign.EndDate = request.EndDate;
			campaign.LastUpdatedBy = userId;
			campaign.LastUpdatedTime = DateTimeOffset.UtcNow;

			// Update class associations
			var currentClassIds = campaign.VaccinationCampaignClasses
				.Where(vcc => vcc.DeletedTime == null) // Chỉ lấy những class chưa bị soft delete
				.Select(vcc => vcc.SchoolClassId)
				.ToList();
			var newClassIds = request.ClassIds.ToList();

			// Remove classes that are no longer in the request
			var classesToRemove = campaign.VaccinationCampaignClasses
				.Where(vcc => vcc.DeletedTime == null && !newClassIds.Contains(vcc.SchoolClassId))
				.ToList();
			foreach (var classToRemove in classesToRemove)
			{
				classToRemove.DeletedBy = userId;
				classToRemove.DeletedTime = DateTimeOffset.UtcNow;
			}

			// Add new classes
			var classesToAdd = newClassIds
				.Where(classId => !currentClassIds.Contains(classId))
				.Select(classId => new VaccinationCampaignClass
				{
					VaccinationCampaignId = vaccinationCampaignId,
					SchoolClassId = classId,
					CreatedBy = userId,
					CreatedTime = DateTimeOffset.UtcNow
				})
				.ToList();

			// Add new class associations to the campaign
			foreach (var classToAdd in classesToAdd)
			{
				campaign.VaccinationCampaignClasses.Add(classToAdd);
			}

			_repositoryManager.VaccinationCampaignRepository.Update(campaign);
			await _repositoryManager.SaveAsync();
			return true;
		}

		public async Task<bool> DeleteVaccinationCampaignAsync(string vaccinationCampaignId, string userId)
		{
			var campaign = _repositoryManager.VaccinationCampaignRepository
				.FindByCondition(vc => vc.Id == vaccinationCampaignId && vc.Status == ApprovalStatus.Pending, true)
				.FirstOrDefault();
			if (campaign == null) return false;

			var user = _repositoryManager.UserRepository
				.FindByCondition(u => u.Id == userId, false)
				.Include(u => u.Role)
				.FirstOrDefault();
			if (campaign.CreatedBy != userId && (user?.Role?.RoleName != "Admin" && user?.Role?.RoleName != "Manager"))
			{
				return false;
			}

			campaign.DeletedBy = userId;
			campaign.DeletedTime = DateTimeOffset.UtcNow;
			_repositoryManager.VaccinationCampaignRepository.Update(campaign);
			await _repositoryManager.SaveAsync();
			return true;
		}
	}
}
