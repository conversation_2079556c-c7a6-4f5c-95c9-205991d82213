{"format": 1, "restore": {"D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {}}, "projects": {"D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj", "projectName": "SMMS.Domain", "projectPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Domain\\SMMS.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Users\\<USER>\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204/PortableRuntimeIdentifierGraph.json"}}}}}