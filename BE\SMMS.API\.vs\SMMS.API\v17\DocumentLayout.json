{"Version": 1, "WorkspaceRootPath": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.api\\controllers\\medicaleventcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|solutionrelative:smms.api\\controllers\\medicaleventcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|solutionrelative:smms.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\services\\implements\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\services\\implements\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\services\\interfaces\\iauthservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\services\\interfaces\\iauthservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|solutionrelative:smms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\helpers\\implements\\importservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\helpers\\implements\\importservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\helpers\\implements\\jwttokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\helpers\\implements\\jwttokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\services\\implements\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\services\\implements\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\services\\implements\\medicalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\services\\implements\\medicalservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.application\\services\\implements\\healthactivityservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FEB29230-3897-47FE-8E9E-097A09C78B49}|SMMS.Application\\SMMS.Application.csproj|solutionrelative:smms.application\\services\\implements\\healthactivityservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|d:\\fpt\\se8\\swp\\swp391_smms_dev2\\be\\smms.api\\smms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EED8A20A-6943-403C-9F5A-C3F88A52FF86}|SMMS.API\\SMMS.API.csproj|solutionrelative:smms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MedicalEventController.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\Controllers\\MedicalEventController.cs", "RelativeDocumentMoniker": "SMMS.API\\Controllers\\MedicalEventController.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\Controllers\\MedicalEventController.cs", "RelativeToolTip": "SMMS.API\\Controllers\\MedicalEventController.cs", "ViewState": "AQIAACAAAAAAAAAAAAAUwF4AAAAwAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T04:53:19.158Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AuthService.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\AuthService.cs", "RelativeDocumentMoniker": "SMMS.Application\\Services\\Implements\\AuthService.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\AuthService.cs", "RelativeToolTip": "SMMS.Application\\Services\\Implements\\AuthService.cs", "ViewState": "AQIAAPMAAAAAAAAAAAAswA0BAAA1AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T01:34:25.43Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IAuthService.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Interfaces\\IAuthService.cs", "RelativeDocumentMoniker": "SMMS.Application\\Services\\Interfaces\\IAuthService.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Interfaces\\IAuthService.cs", "RelativeToolTip": "SMMS.Application\\Services\\Interfaces\\IAuthService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABAAAAA9AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T01:33:44.792Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:11:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:2:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:3:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:4:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:12:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AuthController.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "SMMS.API\\Controllers\\AuthController.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\Controllers\\AuthController.cs", "RelativeToolTip": "SMMS.API\\Controllers\\AuthController.cs", "ViewState": "AQIAAAcAAAAAAAAAAAAmwBYAAAAwAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T01:33:40.202Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ImportService.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Helpers\\Implements\\ImportService.cs", "RelativeDocumentMoniker": "SMMS.Application\\Helpers\\Implements\\ImportService.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Helpers\\Implements\\ImportService.cs", "RelativeToolTip": "SMMS.Application\\Helpers\\Implements\\ImportService.cs", "ViewState": "AQIAABABAAAAAAAAAAAAAC8BAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:28:31.671Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "JwtTokenGenerator.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Helpers\\Implements\\JwtTokenGenerator.cs", "RelativeDocumentMoniker": "SMMS.Application\\Helpers\\Implements\\JwtTokenGenerator.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Helpers\\Implements\\JwtTokenGenerator.cs", "RelativeToolTip": "SMMS.Application\\Helpers\\Implements\\JwtTokenGenerator.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:28:31.039Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "UserService.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\UserService.cs", "RelativeDocumentMoniker": "SMMS.Application\\Services\\Implements\\UserService.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\UserService.cs", "RelativeToolTip": "SMMS.Application\\Services\\Implements\\UserService.cs", "ViewState": "AQIAAEsAAAAAAAAAAAAAABUAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T17:27:24.48Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "MedicalService.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\MedicalService.cs", "RelativeDocumentMoniker": "SMMS.Application\\Services\\Implements\\MedicalService.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\MedicalService.cs", "RelativeToolTip": "SMMS.Application\\Services\\Implements\\MedicalService.cs", "ViewState": "AQIAAHgEAAAAAAAAAAAAAPwAAAAuAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:13:38.383Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "HealthActivityService.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\HealthActivityService.cs", "RelativeDocumentMoniker": "SMMS.Application\\Services\\Implements\\HealthActivityService.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.Application\\Services\\Implements\\HealthActivityService.cs", "RelativeToolTip": "SMMS.Application\\Services\\Implements\\HealthActivityService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T16:13:37.084Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.json", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\appsettings.json", "RelativeDocumentMoniker": "SMMS.API\\appsettings.json", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\appsettings.json", "RelativeToolTip": "SMMS.API\\appsettings.json", "ViewState": "AQIAABgAAAAAAAAAAAAAACIAAABaAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T14:43:19.783Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Program.cs", "DocumentMoniker": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\Program.cs", "RelativeDocumentMoniker": "SMMS.API\\Program.cs", "ToolTip": "D:\\FPT\\SE8\\SWP\\SWP391_SMMS_Dev2\\BE\\SMMS.API\\SMMS.API\\Program.cs", "RelativeToolTip": "SMMS.API\\Program.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T14:43:19.098Z"}]}]}]}