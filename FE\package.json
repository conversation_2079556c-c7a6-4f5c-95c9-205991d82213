{"name": "school-healthcare", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@heroicons/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@react-oauth/google": "^0.12.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^10.12.0", "flatpickr": "^4.6.13", "framer-motion": "^12.15.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-date-picker": "^11.0.0", "react-dom": "^18.3.1", "react-quill": "^2.0.0", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.15.24", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwind-scrollbar": "^3.0.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-commonjs": "^0.10.4", "vite-plugin-svgr": "^4.3.0"}}