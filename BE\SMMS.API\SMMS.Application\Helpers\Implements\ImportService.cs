﻿using ClosedXML.Excel;
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using SMMS.Domain.Entity;
using SMMS.Domain.Interface.Repositories;



namespace SMMS.Application.Helpers.Implements
{
	public class ImportService
	{
		private readonly IRepositoryManager _repositoryManager;

		public ImportService(IRepositoryManager repositoryManager)
		{
			_repositoryManager = repositoryManager;
		}

		public async Task ImportStudentsFromExcelAsync(Stream fileStream)
		{
			using var workbook = new XLWorkbook(fileStream);
			var worksheet = workbook.Worksheet(1); // Giả sử dữ liệu nằm ở sheet đầu tiên

			var rows = worksheet.RowsUsed().Skip(1); // Bỏ qua hàng tiêu đề

			var emailTasks = new List<Task>();

			// Dictionary tạm để lưu các class và user vừa tạo/truy xuất trong phiên import
			var classDict = new Dictionary<string, SchoolClass>(); // key: className
			var userDict = new Dictionary<string, User>(); // key: parentEmail hoặc parentPhone

			foreach (var row in rows)
			{
				var parentEmail = row.Cell(1).GetString();
				var parentPhone = row.Cell(2).GetString();
				var parentFullName = row.Cell(3).GetString();
				var studentFullName = row.Cell(4).GetString();
				var studentGender = row.Cell(5).GetString();
				var studentDateOfBirth = row.Cell(6).GetDateTime();
				var className = row.Cell(7).GetString();

				// --- SchoolClass ---
				SchoolClass? schoolClass = null;
				if (classDict.ContainsKey(className))
				{
					schoolClass = classDict[className];
				}
				else
				{
					// Tìm trong DB (bao gồm cả soft-delete)
					schoolClass = _repositoryManager.ClassRepository
						.FindByCondition(c => c.ClassName == className, true)
						.FirstOrDefault();
					if (schoolClass == null)
					{
						schoolClass = new SchoolClass
						{
							Id = Guid.NewGuid().ToString(),
							ClassName = className,
							ClassRoom = className,
							CreatedBy = "System",
							CreatedTime = DateTimeOffset.UtcNow
						};
						_repositoryManager.ClassRepository.Create(schoolClass);
					}
					else if (schoolClass.DeletedBy != null)
					{
						// Khôi phục nếu bị soft-delete
						schoolClass.DeletedBy = null;
						schoolClass.DeletedTime = null;
						schoolClass.LastUpdatedBy = "System";
						schoolClass.LastUpdatedTime = DateTimeOffset.UtcNow;
						_repositoryManager.ClassRepository.Update(schoolClass);
					}
					classDict[className] = schoolClass;
				}

				// --- Parent User ---
				string parentKey = !string.IsNullOrEmpty(parentEmail) ? parentEmail : parentPhone;
				User? parentUser = null;
				if (userDict.ContainsKey(parentKey))
				{
					parentUser = userDict[parentKey];
				}
				else
				{
					// Tìm trong DB (bao gồm cả soft-delete)
					parentUser = _repositoryManager.UserRepository
						.FindByCondition(u => (u.Email == parentEmail || u.Phone == parentPhone), true)
						.FirstOrDefault();

					if (parentUser == null)
					{
						// Case: Parent is completely new
						var parentRole = _repositoryManager.RoleRepository
							.FindByCondition(r => r.RoleName == "Parent", false)
							.FirstOrDefault();
						if (parentRole == null)
						{
							throw new Exception("Role 'Parent' not found.");
						}

						// Mật khẩu mặc định
						var defaultPassword = GenerateRandomPassword(6);

						parentUser = new User
						{
							Id = Guid.NewGuid().ToString(),
							Email = parentEmail,
							Phone = parentPhone,
							FullName = parentFullName,
							RoleId = parentRole.Id,
							Password = BCrypt.Net.BCrypt.HashPassword(defaultPassword),
							CreatedBy = "System",
							CreatedTime = DateTimeOffset.UtcNow
						};
						_repositoryManager.UserRepository.Create(parentUser);
						emailTasks.Add(SendLoginDetailsEmailAsync(parentEmail, parentFullName, defaultPassword));
					}
					else if (parentUser.DeletedBy != null && parentUser.DeletedTime != null)
					{
						// Case: Parent exists but is soft-deleted
						parentUser.DeletedTime = null;
						parentUser.DeletedBy = null;
						parentUser.Phone = parentPhone;
						parentUser.Email = parentEmail;
						parentUser.FullName = parentFullName;
						parentUser.LastUpdatedBy = "System";
						parentUser.LastUpdatedTime = DateTimeOffset.UtcNow;
						_repositoryManager.UserRepository.Update(parentUser);
					}
					else if (parentUser.Phone != parentPhone || parentUser.FullName != parentFullName || parentUser.Email != parentEmail)
					{
						// Case: Parent exists, not soft-deleted, nhưng thông tin khác
						parentUser.Phone = parentPhone;
						parentUser.Email = parentEmail;
						parentUser.FullName = parentFullName;
						parentUser.LastUpdatedBy = "System";
						parentUser.LastUpdatedTime = DateTimeOffset.UtcNow;
						_repositoryManager.UserRepository.Update(parentUser);
					}
					userDict[parentKey] = parentUser;
				}

				// --- Student ---
				var student = _repositoryManager.StudentRepository
					.FindByCondition(s => s.FullName == studentFullName && s.DateOfBirth == studentDateOfBirth && s.DeletedBy == null, true)
					.FirstOrDefault();

				User? oldParent = null;
				if (student != null)
				{
					oldParent = _repositoryManager.UserRepository
						.FindByCondition(u => u.Id == student.ParentId && u.DeletedBy == null, true)
						.FirstOrDefault();
				}

				if (student == null)
				{
					// Case: Student is new
					student = new Student
					{
						Id = Guid.NewGuid().ToString(),
						ParentId = parentUser.Id,
						ClassId = schoolClass.Id,
						FullName = studentFullName,
						Gender = studentGender,
						DateOfBirth = studentDateOfBirth,
						CreatedBy = "System",
						CreatedTime = DateTimeOffset.UtcNow
					};
					_repositoryManager.StudentRepository.Create(student);
				}
				else
				{
					// Case: Student exists, check for updates
					bool hasChanges = false;
					if (student.ClassId != schoolClass.Id ||
						student.Gender != studentGender ||
						student.DateOfBirth != studentDateOfBirth ||
						student.ParentId != parentUser.Id)
					{
						hasChanges = true;
						student.ClassId = schoolClass.Id;
						student.Gender = studentGender;
						student.DateOfBirth = studentDateOfBirth;
						student.ParentId = parentUser.Id;
						student.LastUpdatedBy = "System";
						student.LastUpdatedTime = DateTimeOffset.UtcNow;
						_repositoryManager.StudentRepository.Update(student);
					}
					// Handle old parent soft-deletion if parent has changed and old parent exists
					if (hasChanges && oldParent != null && oldParent.Id != parentUser.Id)
					{
						// Check if old parent is still associated with other students
						var otherStudents = _repositoryManager.StudentRepository
							.FindByCondition(s => s.ParentId == oldParent.Id && s.Id != student.Id && s.DeletedBy == null, false)
							.Any();
						if (!otherStudents)
						{
							// Soft-delete old parent if not linked to other students
							oldParent.DeletedBy = "System";
							oldParent.DeletedTime = DateTimeOffset.UtcNow;
							_repositoryManager.UserRepository.Update(oldParent);
						}
					}
				}
			}
			await _repositoryManager.SaveAsync();
			await Task.WhenAll(emailTasks);
		}

		private string GenerateRandomPassword(int length)
		{
			const string validChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
			var random = new Random();
			var password = new char[length];

			for (int i = 0; i < length; i++)
			{
				password[i] = validChars[random.Next(validChars.Length)];
			}

			return new string(password);
		}

		private async Task SendLoginDetailsEmailAsync(string email, string fullName, string password)
		{
			var message = new MimeMessage();
			message.From.Add(new MailboxAddress("SMMS", "<EMAIL>"));
			message.To.Add(MailboxAddress.Parse(email));
			message.Subject = "Thông tin đăng nhập SMMS";
			message.Body = new TextPart("html")
			{
				Text = $@"
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    background-color: #f4f4f9;
                    padding: 20px;
                }}
                .email-container {{
                    background-color: #ffffff;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }}
                .email-header {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                }}
                .email-body {{
                    font-size: 16px;
                    color: #555;
                }}
                .footer {{
                    font-size: 12px;
                    color: #aaa;
                    margin-top: 20px;
                }}
                .button {{
                    display: inline-block;
                    background-color: #007bff;
                    color: white;
                    padding: 10px 20px;
                    text-decoration: none;
                    border-radius: 5px;
                }}
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='email-header'>Xin chào {fullName},</div>
                <div class='email-body'>
                    <p>Chào mừng bạn đến với hệ thống SMMS.</p>
                    <p>Tài khoản của bạn đã được tạo thành công.</p>
                    <p><strong>Thông tin đăng nhập của bạn:</strong></p>
                    <p><strong>Email:</strong> {email}</p>
                    <p><strong>Mật khẩu:</strong> {password}</p>
                    <p>Vui lòng đăng nhập và thay đổi mật khẩu của bạn sau khi đăng nhập.</p>
                </div>
                <div class='footer'>
                    Trân trọng,<br>
                    SMMS Team
                </div>
            </div>
        </body>
        </html>"
			};

			using var client = new SmtpClient();
			await client.ConnectAsync("smtp.gmail.com", 587, SecureSocketOptions.StartTls);
			await client.AuthenticateAsync("<EMAIL>", "gnmjhwhbyoovvigw");
			await client.SendAsync(message);
			await client.DisconnectAsync(true);
		}
	}
}
