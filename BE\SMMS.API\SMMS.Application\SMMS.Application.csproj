﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="CloudinaryDotNet" Version="1.27.5" />
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="MailKit" Version="4.13.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SMMS.Domain\SMMS.Domain.csproj" />
    <ProjectReference Include="..\SMMS.Infrastructure\SMMS.Infrastructure.csproj" />
  </ItemGroup>

</Project>
