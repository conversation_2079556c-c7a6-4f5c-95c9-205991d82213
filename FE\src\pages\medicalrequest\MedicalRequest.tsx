import { TaskIcon } from "@/components/icons";
import Label from "@/components/ui/form/Label";
import PageHeader from "@/components/ui/PageHeader";
import { FecthParents, FecthStudentsByParentId } from "@/services/UserService";
import { ParentViewModel } from "@/types/User";
import { Student } from "@/types/Student";
import { Search, X, Users, ArrowRight, FilePlusIcon } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import SearchableSelect from "@/components/ui/form/SearchableSelect";
import MultiMedicationModal from "@/components/medicalrequest/MultiMedicationModal";
import {
  FecthMedicalRequest,
  FecthMedicalRequestById,
} from "@/services/MedicalRequest";
import {
  ListMedicalRequestViewModel,
  MedicalRequestViewModel,
} from "@/types/MedicalRequest";
import { Modal } from "@/components/ui/modal";
import UpdateMedicationModal from "@/components/medicalrequest/UpdateMedicationModal";
import { FecthUpdateMedicalRequest } from "@/services/MedicalRequest";
import DeleteConfirmationModal from "@/components/medicalrequest/DeleteConfirmationModal";
import { FecthDeleteMedicalRequest } from "@/services/MedicalRequest";
import { DecodeJWT } from "@/utils/DecodeJWT";
import { FecthHealthProfile } from "@/services/HealthProfileService";
import { showToast } from "@/components/ui/Toast";
import ApiClient from "@/utils/ApiBase";

export default function MedicalRequest() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [parents, setParents] = useState<ParentViewModel[]>([]);
  const [filteredParents, setFilteredParents] = useState<ParentViewModel[]>([]);
  const [loading, setLoading] = useState(false);
  // State cho filter Admin - chọn phụ huynh để xem danh sách học sinh
  const [selectedParentForFilter, setSelectedParentForFilter] =
    useState<ParentViewModel | null>(null);
  const [students, setStudents] = useState<Student[]>([]);
  const [studentsLoading, setStudentsLoading] = useState(false);

  const [studentOptions] = useState<{ value: string; label: string }[]>([]);
  const [studentsLoadingForSearch, setStudentsLoadingForSearch] =
    useState(false);

  const [showMedicationModal, setShowMedicationModal] = useState(false);
  const [selectedStudentForModal, setSelectedStudentForModal] =
    useState<Student | null>(null);
  const [selectedStudentId, setSelectedStudentId] = useState<string>("");

  const [medicalRequests, setMedicalRequests] = useState<
    ListMedicalRequestViewModel[]
  >([]);
  const [medicalRequestsLoading, setMedicalRequestsLoading] = useState(false);

  const [showDetailModal, setShowDetailModal] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [medicalRequestDetail, setMedicalRequestDetail] =
    useState<MedicalRequestViewModel | null>(null);

  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [updateRequest, setUpdateRequest] = useState<Record<string, unknown>>(
    {}
  );
  const [selectedDeleteRequest, setSelectedDeleteRequest] =
    useState<ListMedicalRequestViewModel | null>(null);

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Thêm state cho AddMedicationModal
  const [showAddMedicationModal, setShowAddMedicationModal] = useState(false);

  // Thay đổi từ boolean sang string để lưu role
  const [role, setRole] = useState<string>("");

  // Thêm state lưu parentId đã fetch
  const [selectedParentIdForModal, setSelectedParentIdForModal] =
    useState<string>("");

  // Kiểm tra role khi component mount
  useEffect(() => {
    const decodedToken = DecodeJWT();
    if (decodedToken) {
      const r =
        decodedToken[
          "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
        ];
      setRole(r ?? "");
    }
  }, []);

  // Hàm duy nhất để xử lý chọn học sinh và fetch parentId
  const handleChooseStudent = useCallback(async (student: Student) => {
    if (!student) return;

    // 1) Chọn student
    setSelectedStudentForModal(student);
    setSelectedStudentId(student.id);

    // 2) Fetch parentId background
    try {
      const response = await ApiClient<{ parentId: string }>({
        method: "GET",
        endpoint: `/medical-request/parent-id/${student.id}`,
      });

      setSelectedParentIdForModal(response.data.parentId);
    } catch (error) {
      console.error("Failed to fetch parentId:", error);
      showToast.error("Không lấy được thông tin phụ huynh");
      setSelectedParentIdForModal("");
    }
  }, []);

  const fetchParent = useCallback(async () => {
    // Nếu là Parent, không cần fetch danh sách phụ huynh
    if (role === "Parent") {
      return;
    }

    setLoading(true);
    try {
      const response = await FecthParents();
      setParents(response || []);
      setFilteredParents(response || []);
    } catch (err) {
      console.error("Failed to fetch parent:", err);
      setParents([]);
      setFilteredParents([]);
    } finally {
      setLoading(false);
    }
  }, [role]);

  const fetchStudentsByParentId = useCallback(async (parentId: string) => {
    setStudentsLoading(true);
    try {
      const response = await FecthStudentsByParentId(parentId);
      setStudents(response || []);
    } catch (err) {
      console.error("Failed to fetch students by parent ID:", err);
      setStudents([]);
    } finally {
      setStudentsLoading(false);
    }
  }, []);

  // Thêm function để fetch danh sách con của Parent đang đăng nhập
  const fetchParentChildren = useCallback(async () => {
    if (role !== "Parent") return;

    setStudentsLoading(true);
    try {
      const response = await FecthHealthProfile();
      // Cast type để tương thích với interface Student từ types/Student.ts
      setStudents((response as Student[]) || []);
    } catch (err) {
      console.error("Failed to fetch parent children:", err);
      setStudents([]);
    } finally {
      setStudentsLoading(false);
    }
  }, [role]);

  const handleParentClick = useCallback(
    (parent: ParentViewModel) => {
      setSelectedParentForFilter(parent);
      fetchStudentsByParentId(parent.id);
    },
    [fetchStudentsByParentId]
  );

  const handleNavigateToStudent = useCallback(() => {
    if (selectedStudentId) {
      navigate(
        `/dashboard/medical/manager-medical-request/${selectedStudentId}`
      );
    }
  }, [selectedStudentId, navigate]);

  const filterParents = useCallback(() => {
    if (!searchTerm || searchTerm.trim() === "") {
      setFilteredParents([]);
      return;
    }

    const filtered = parents.filter(
      (parent) =>
        parent.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        parent.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        parent.phone?.includes(searchTerm)
    );

    setFilteredParents(filtered);
  }, [parents, searchTerm]);

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedStudentId("");
    setSelectedParentIdForModal("");
    setSelectedStudentForModal(null);
  };

  const fetchMedicalRequests = useCallback(async () => {
    setMedicalRequestsLoading(true);
    try {
      const data = await FecthMedicalRequest();
      setMedicalRequests(data || []);
    } catch {
      setMedicalRequests([]);
    } finally {
      setMedicalRequestsLoading(false);
    }
  }, []);

  const handleShowDetail = async (id: string) => {
    setDetailLoading(true);
    setShowDetailModal(true);
    try {
      const detail = await FecthMedicalRequestById(id);
      setMedicalRequestDetail(detail);
    } catch {
      setMedicalRequestDetail(null);
    } finally {
      setDetailLoading(false);
    }
  };

  const handleEdit = () => {
    if (!medicalRequestDetail) return;
    setUpdateRequest({
      parentName: medicalRequestDetail.parentName,
      phoneNumber: medicalRequestDetail.phoneNumber,
      studentName: medicalRequestDetail.studentName,
      medicationName: medicalRequestDetail.medicationName,
      form: "",
      dosage: "",
      route: "",
      frequency: 1,
      totalQuantity: medicalRequestDetail.totalQuantity,
      timeToAdminister: medicalRequestDetail.timeToAdminister,
      startDate: medicalRequestDetail.startDate,
      endDate: medicalRequestDetail.endDate,
      note: medicalRequestDetail.notes,
    });
    setShowUpdateModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!medicalRequestDetail) return;
    try {
      // Map lại dữ liệu đúng format API (camelCase)
      const payload = {
        studentId: medicalRequestDetail.studentId,
        parentId: medicalRequestDetail.parentId,
        medicalRequestItems: [
          {
            medicationName: String(updateRequest.medicationName ?? ""),
            form: String(updateRequest.form ?? ""),
            dosage: String(updateRequest.dosage ?? ""),
            route: String(updateRequest.route ?? ""),
            frequency: Number(updateRequest.frequency ?? 1),
            totalQuantity: Number(updateRequest.totalQuantity ?? 0),
            timeToAdminister: Array.isArray(updateRequest.timeToAdminister)
              ? updateRequest.timeToAdminister.map(String)
              : [],
            startDate: String(updateRequest.startDate ?? ""),
            endDate: String(updateRequest.endDate ?? ""),
            notes: String(updateRequest.note ?? ""),
          },
        ],
      };
      await FecthUpdateMedicalRequest(medicalRequestDetail.id, payload);
      setShowUpdateModal(false);
      setShowDetailModal(false);
      await fetchMedicalRequests();
      await handleShowDetail(medicalRequestDetail.id);
    } catch (err: unknown) {
      if (err instanceof Error) {
        alert(err.message || "Cập nhật đơn thuốc thất bại");
      }
    }
  };

  const handleDeleteClick = (req: ListMedicalRequestViewModel) => {
    setSelectedDeleteRequest(req);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedDeleteRequest) return;
    try {
      await FecthDeleteMedicalRequest(selectedDeleteRequest.id);
      setShowDeleteModal(false);
      await fetchMedicalRequests();
    } catch (err: unknown) {
      if (err instanceof Error) {
        alert(err.message || "Xóa đơn thuốc thất bại");
      }
    } finally {
      setSelectedDeleteRequest(null);
    }
  };

  const fetchAllStudents = useCallback(async () => {
    setStudentsLoadingForSearch(true);
    try {
      // Giả sử bạn có API lấy toàn bộ học sinh
      // const response = await FecthStudents();
      // setAllStudents(response || []);
      // setStudentOptions(...)
    } finally {
      setStudentsLoadingForSearch(false);
    }
  }, []);

  // Nút tạo đơn thuốc: chỉ mở modal khi có đủ thông tin
  const handleOpenAddMedicationModal = () => {
    if (!selectedStudentForModal || !selectedParentIdForModal) {
      showToast.error("Vui lòng chọn học sinh trước!");
      return;
    }

    if (role === "Parent") {
      setShowAddMedicationModal(true);
    } else {
      setShowMedicationModal(true);
    }
  };

  useEffect(() => {
    fetchParent();
    fetchAllStudents();
    fetchMedicalRequests();
    fetchParentChildren(); // Fetch children when component mounts
  }, [
    fetchParent,
    fetchAllStudents,
    fetchMedicalRequests,
    fetchParentChildren,
  ]);

  useEffect(() => {
    filterParents();
  }, [filterParents]);

  return (
    <div className="p-6">
      <PageHeader
        title="Yêu cầu y tế"
        icon={<TaskIcon className="w-10 h-10" />}
        description={
          role === "Parent"
            ? "Quản lý đơn thuốc cho con của bạn"
            : "Yêu cầu dịch vụ y tế từ phụ huynh"
        }
      />

      {/* Chỉ hiển thị bộ lọc tìm kiếm khi không phải Parent */}
      {role !== "Parent" && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-5 mb-6">
          <div className="flex flex-col gap-5">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                Bộ lọc tìm kiếm
              </h2>
              {(searchTerm || selectedStudentId) && (
                <button
                  onClick={handleClearFilters}
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Xóa bộ lọc
                </button>
              )}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <Label htmlFor="search">Tìm kiếm phụ huynh</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="search"
                    placeholder="Tìm kiếm theo tên phụ huynh, email, số điện thoại..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                    }}
                    className="block w-full pl-10 pr-3 py-2.5 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-400 transition-colors"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm("")}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>
              <div>
                <Label htmlFor="student-search">Tìm kiếm học sinh</Label>
                <div className="flex flex-wrap gap-2 items-start">
                  {/* Ô chọn học sinh */}
                  <div className="flex-1 min-w-[200px]">
                    <SearchableSelect
                      options={studentOptions}
                      placeholder={
                        studentsLoadingForSearch
                          ? "Đang tải..."
                          : "Chọn học sinh theo mã hoặc tên..."
                      }
                      onChange={(studentId) => {
                        const student = students.find(
                          (s) => s.id === studentId
                        );
                        if (student) {
                          handleChooseStudent(student);
                        }
                      }}
                      value={selectedStudentId}
                    />
                  </div>

                  {/* Các nút chức năng */}
                  <div className="flex flex-row gap-3 items-start">
                    {/* Nút xem đơn thuốc */}
                    <div className="flex flex-col gap-2 items-stretch">
                      <button
                        onClick={handleNavigateToStudent}
                        disabled={
                          !selectedStudentId || studentsLoadingForSearch
                        }
                        className="inline-flex items-center gap-2 px-4 py-2 h-[44px] text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition"
                      >
                        <ArrowRight className="w-4 h-4" />
                        Xem đơn thuốc
                      </button>
                    </div>
                    {/* Cụm 2 nút dọc */}
                    <div className="flex flex-col gap-2 items-stretch">
                      <button
                        onClick={handleOpenAddMedicationModal}
                        disabled={
                          !selectedStudentForModal ||
                          !selectedParentIdForModal ||
                          studentsLoadingForSearch
                        }
                        className="inline-flex items-center gap-2 px-4 py-2 h-[44px] text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition"
                      >
                        <FilePlusIcon className="w-4 h-4" />
                        Tạo đơn thuốc
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {(searchTerm || selectedStudentId) && (
              <div className="flex items-center gap-2 text-sm text-gray-600 pt-2">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>
                  {searchTerm && (
                    <>
                      Hiển thị kết quả phụ huynh cho{" "}
                      <span className="font-medium">"{searchTerm}"</span>
                    </>
                  )}
                  {searchTerm && selectedStudentId && " | "}
                  {selectedStudentId && (
                    <>
                      Đã chọn học sinh:{" "}
                      <span className="font-medium">
                        {studentOptions.find(
                          (opt) => opt.value === selectedStudentId
                        )?.label || "N/A"}
                      </span>
                    </>
                  )}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Parent Results Section - Chỉ hiển thị khi không phải Parent */}
      {role !== "Parent" && (
        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-1 bg-white rounded-xl shadow-sm border border-gray-200 p-5">
            <div className="flex items-center justify-between mb-5">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Users className="w-5 h-5" />
                Danh sách phụ huynh
                {filteredParents.length > 0 && (
                  <span className="text-sm font-normal text-gray-500">
                    ({filteredParents.length} kết quả)
                  </span>
                )}
              </h2>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-gray-600">Đang tải...</span>
              </div>
            ) : filteredParents.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">
                  {searchTerm
                    ? "Không tìm thấy phụ huynh nào phù hợp với từ khóa tìm kiếm"
                    : "Nhập từ khóa để tìm kiếm phụ huynh"}
                </p>
                {searchTerm && (
                  <p className="text-xs text-gray-400 mt-2">
                    Tổng số phụ huynh: {parents.length} | Đã lọc:{" "}
                    {filteredParents.length}
                  </p>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {filteredParents.map((parent, index) => (
                  <div
                    key={index}
                    onClick={() => handleParentClick(parent)}
                    className={`border rounded-lg p-4 hover:shadow-md transition-all cursor-pointer ${
                      selectedParentForFilter?.id === parent.id
                        ? "border-blue-500 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-blue-300"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">
                          {parent.fullName}
                        </h3>
                        <p className="text-sm text-gray-500 truncate">
                          {parent.email}
                        </p>
                        <p className="text-sm text-gray-500">{parent.phone}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Students Results Section */}
          <div className="col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-5">
            <div className="flex items-center justify-between mb-5">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Users className="w-5 h-5" />
                {selectedParentForFilter
                  ? `Danh sách học sinh của ${selectedParentForFilter.fullName}`
                  : "Danh sách học sinh"}
                {students.length > 0 && (
                  <span className="text-sm font-normal text-gray-500">
                    ({students.length} học sinh)
                  </span>
                )}
              </h2>
              {selectedParentForFilter && (
                <button
                  onClick={() => {
                    setSelectedParentForFilter(null);
                    setStudents([]);
                  }}
                  className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Đóng
                </button>
              )}
            </div>

            {studentsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-gray-600">
                  Đang tải danh sách học sinh...
                </span>
              </div>
            ) : !selectedParentForFilter ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">
                  Vui lòng chọn phụ huynh để xem danh sách học sinh
                </p>
              </div>
            ) : students.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">
                  Không tìm thấy học sinh nào cho phụ huynh này
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {students.map((student, index) => (
                  <div
                    key={index}
                    onClick={() => handleChooseStudent(student)}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer hover:border-blue-300"
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-green-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">
                          {student.fullName}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Mã học sinh: {student.studentCode}
                        </p>
                        <p className="text-sm text-gray-500">
                          Giới tính: {student.gender}
                        </p>
                        <p className="text-sm text-gray-500">
                          Ngày sinh:{" "}
                          {new Date(student.dateOfBirth).toLocaleDateString(
                            "vi-VN"
                          )}
                        </p>
                        {student.studentClass && (
                          <div className="mt-2 pt-2 border-t border-gray-100">
                            <p className="text-xs text-gray-400 mb-1">
                              Lớp học:
                            </p>
                            <p className="text-sm font-medium text-gray-700">
                              {student.studentClass.className}
                            </p>
                            <p className="text-xs text-gray-500">
                              Phòng: {student.studentClass.classRoom}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Students Section cho Parent - Hiển thị trực tiếp danh sách con */}
      {role === "Parent" && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-5 mb-6">
          <div className="flex items-center justify-between mb-5">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Users className="w-5 h-5" />
              Danh sách con của bạn
              {students.length > 0 && (
                <span className="text-sm font-normal text-gray-500">
                  ({students.length} học sinh)
                </span>
              )}
            </h2>
          </div>

          {studentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600">
                Đang tải danh sách con...
              </span>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">
                Không tìm thấy học sinh nào trong danh sách con của bạn
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {students.map((student, index) => (
                  <div
                    key={index}
                    onClick={() => handleChooseStudent(student)}
                    className={`border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer hover:border-blue-300 ${
                      selectedStudentForModal?.id === student.id
                        ? "ring-2 ring-blue-500"
                        : ""
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-green-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">
                          {student.fullName}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Mã học sinh: {student.studentCode}
                        </p>
                        <p className="text-sm text-gray-500">
                          Giới tính: {student.gender}
                        </p>
                        <p className="text-sm text-gray-500">
                          Ngày sinh:{" "}
                          {new Date(student.dateOfBirth).toLocaleDateString(
                            "vi-VN"
                          )}
                        </p>
                        {student.studentClass && (
                          <div className="mt-2 pt-2 border-t border-gray-100">
                            <p className="text-xs text-gray-400 mb-1">
                              Lớp học:
                            </p>
                            <p className="text-sm font-medium text-gray-700">
                              {student.studentClass.className}
                            </p>
                            <p className="text-xs text-gray-500">
                              Phòng: {student.studentClass.classRoom}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {/* Nút tạo đơn thuốc chỉ hiện khi đã chọn học sinh */}
              <div className="flex justify-end mt-4">
                <button
                  onClick={handleOpenAddMedicationModal}
                  disabled={
                    !selectedStudentForModal || !selectedParentIdForModal
                  }
                  className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition"
                >
                  <FilePlusIcon className="w-4 h-4" />
                  Tạo đơn thuốc
                </button>
              </div>
            </>
          )}
        </div>
      )}
      {/* Multi Medication Modal */}
      <MultiMedicationModal
        isOpen={showMedicationModal}
        parentId={selectedParentIdForModal}
        studentId={selectedStudentForModal?.id || ""}
        onClose={() => {
          setShowMedicationModal(false);
          setSelectedStudentForModal(null);
          setSelectedParentIdForModal("");
        }}
        selectedStudent={selectedStudentForModal}
        onSubmit={() => {
          fetchMedicalRequests(); // Refresh list after create
        }}
      />

      {/* Danh sách đơn thuốc */}
      <div className="mt-10">
        <h2 className="text-lg font-semibold mb-4">Danh sách đơn thuốc</h2>
        {medicalRequestsLoading ? (
          <div>Đang tải...</div>
        ) : medicalRequests.length === 0 ? (
          <div>Không có đơn thuốc nào.</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full table-fixed border border-gray-200">
              <thead>
                <tr>
                  <th className="px-4 py-2 text-left w-40">Học sinh</th>
                  <th className="px-4 py-2 text-left w-28">Lớp</th>
                  <th className="px-4 py-2 text-left w-40">Phụ huynh</th>
                  <th className="px-4 py-2 text-left w-40">Thuốc</th>
                  <th className="px-4 py-2 text-center w-24">Số lượng tổng</th>
                  <th className="px-4 py-2 text-center w-28">
                    Số lượng còn lại
                  </th>
                  <th className="px-4 py-2 text-center w-24">Trạng thái</th>
                  <th className="px-4 py-2 text-center w-28">Ngày tạo</th>
                  <th className="px-4 py-2 text-center w-20">Hành động</th>
                </tr>
              </thead>
              <tbody>
                {medicalRequests.map((req) => (
                  <tr
                    key={req.id}
                    className="border-b hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleShowDetail(req.id)}
                  >
                    <td className="px-4 py-2 truncate text-left">
                      {req.studentName}
                    </td>
                    <td className="px-4 py-2 truncate text-left">
                      {req.studentClass}
                    </td>
                    <td className="px-4 py-2 truncate text-left">
                      {req.parentName}
                    </td>
                    <td className="px-4 py-2 truncate text-left">
                      {req.medicationName}
                    </td>
                    <td className="px-4 py-2 truncate text-center">
                      {req.totalQuantity}
                    </td>
                    <td className="px-4 py-2 truncate text-center">
                      {req.remainingQuantity}
                    </td>
                    <td className="px-4 py-2 truncate text-center">
                      {req.status === "Active"
                        ? "Đang hoạt động"
                        : req.status === "Inactive"
                        ? "Không hoạt động"
                        : req.status === "Completed"
                        ? "Đã hoàn thành"
                        : req.status}
                    </td>
                    <td className="px-4 py-2 truncate text-center">
                      {new Date(req.createdTime).toLocaleDateString("vi-VN")}
                    </td>
                    <td className="px-4 py-2 truncate text-center">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteClick(req);
                        }}
                        className="text-red-600 hover:underline"
                      >
                        Xóa
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal xem chi tiết đơn thuốc */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => {
          setShowDetailModal(false);
          setMedicalRequestDetail(null);
        }}
      >
        <div className="p-6 min-w-[350px] max-w-[500px]">
          <h3 className="text-lg font-semibold mb-4">Chi tiết đơn thuốc</h3>
          {detailLoading ? (
            <div>Đang tải...</div>
          ) : !medicalRequestDetail ? (
            <div>Không tìm thấy thông tin đơn thuốc.</div>
          ) : (
            <div className="space-y-2">
              <div>
                <b>Học sinh:</b> {medicalRequestDetail.studentName}
              </div>
              <div>
                <b>Lớp:</b> {medicalRequestDetail.studentClass}
              </div>
              <div>
                <b>Phụ huynh:</b> {medicalRequestDetail.parentName}
              </div>
              <div>
                <b>Thuốc:</b> {medicalRequestDetail.medicationName}
              </div>
              <div>
                <b>Số lượng tổng:</b> {medicalRequestDetail.totalQuantity}
              </div>
              <div>
                <b>Số lượng còn lại:</b>{" "}
                {medicalRequestDetail.remainingQuantity}
              </div>
              <div>
                <b>Thời gian dùng:</b>{" "}
                {medicalRequestDetail.timeToAdminister?.join(", ")}
              </div>
              <div>
                <b>Ngày bắt đầu:</b> {medicalRequestDetail.startDate}
              </div>
              <div>
                <b>Ngày kết thúc:</b> {medicalRequestDetail.endDate}
              </div>
              <div>
                <b>Trạng thái:</b> {medicalRequestDetail.status}
              </div>
              <div>
                <b>Ngày tạo:</b>{" "}
                {new Date(medicalRequestDetail.createdTime).toLocaleDateString(
                  "vi-VN"
                )}
              </div>
              <div>
                <b>Ghi chú:</b> {medicalRequestDetail.notes || "-"}
              </div>
              <div className="pt-4 flex justify-end">
                <button
                  onClick={handleEdit}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Sửa
                </button>
              </div>
            </div>
          )}
        </div>
      </Modal>

      {/* Modal cập nhật đơn thuốc */}
      <UpdateMedicationModal
        isOpen={showUpdateModal}
        onClose={() => setShowUpdateModal(false)}
        updateRequest={updateRequest}
        setUpdateRequest={setUpdateRequest}
        onConfirm={handleConfirmUpdate}
        medicationForms={[
          "Viên nén",
          "Siro",
          "Thuốc nhỏ mắt",
          "Kem bôi",
          "Viên con nhộng",
          "Thuốc tiêm",
          "Thuốc mỡ",
          "Thuốc đặt",
          "Thuốc hít",
          "Vắc-xin",
        ]}
      />

      <DeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        selectedRequest={selectedDeleteRequest}
        onConfirm={handleConfirmDelete}
      />

      {/* MultiMedicationModal cho Parent */}
      <MultiMedicationModal
        isOpen={showAddMedicationModal}
        parentId={selectedParentIdForModal}
        studentId={selectedStudentForModal?.id || ""}
        onClose={() => {
          setShowAddMedicationModal(false);
          setSelectedStudentForModal(null);
          setSelectedParentIdForModal("");
        }}
        selectedStudent={selectedStudentForModal}
        onSubmit={async (medications) => {
          // Xử lý tạo đơn thuốc mới
          console.log("Tạo đơn thuốc mới:", medications);
          await fetchMedicalRequests(); // Refresh list after create
        }}
      />
    </div>
  );
}
