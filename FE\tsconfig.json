{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "baseUrl": "./src", "paths": {"@/*": ["*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "composite": true}, "include": ["src"], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}]}