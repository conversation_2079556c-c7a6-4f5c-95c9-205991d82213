﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\SMMS.Domain\SMMS.Domain.csproj" />
  </ItemGroup>

</Project>
