﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SMMS.Infrastructure.Context;

#nullable disable

namespace SMMS.Infrastructure.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20250714041038_NewInit")]
    partial class NewInit
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("SMMS.Domain.Entity.ActivityConsent", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ActivityType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("HealthActivityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("ScheduleTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccinationCampaignId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("HealthActivityId");

                    b.HasIndex("StudentId");

                    b.HasIndex("UserId");

                    b.HasIndex("VaccinationCampaignId");

                    b.ToTable("ActivityConsent");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Blog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Excerpt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("View")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Blog");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.ConselingSchedule", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("HealthCheckupId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicalStaffId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("MeetingDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ParentRejectNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("HealthCheckupId");

                    b.HasIndex("MedicalStaffId");

                    b.HasIndex("ParentId");

                    b.HasIndex("StudentId");

                    b.ToTable("ConselingSchedule");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("HealthActivity");

                    b.HasData(
                        new
                        {
                            Id = "b857d305-8a7c-4f22-b509-e8f2d283084e",
                            CreatedBy = "6c21ab80-b663-4705-9bdc-29cba885ac5a",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3743), new TimeSpan(0, 0, 0, 0, 0)),
                            Description = "Nha Cai Hang Dau So 1 Dong Nam A",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Bet88",
                            ScheduledDate = new DateTime(2024, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Status = 0,
                            UserId = "6c21ab80-b663-4705-9bdc-29cba885ac5a"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivityClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("HealthActivityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SchoolClassId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("HealthActivityId");

                    b.HasIndex("SchoolClassId");

                    b.ToTable("HealthActivityClasses");

                    b.HasData(
                        new
                        {
                            Id = "6220c643-7b77-4f71-b7e5-7e5e579cbbd8",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3814), new TimeSpan(0, 0, 0, 0, 0)),
                            HealthActivityId = "b857d305-8a7c-4f22-b509-e8f2d283084e",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            SchoolClassId = "913409a4-230c-45dd-8d1b-6dcaecda2523"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthCheckupRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AbnormalNote")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("BMI")
                        .HasColumnType("float");

                    b.Property<int>("CheckingStatus")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dental")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HealthActivityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Hearing")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Height")
                        .HasColumnType("float");

                    b.Property<bool>("IsLatest")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("RecordDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.Property<string>("Vision")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Weight")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("HealthActivityId");

                    b.HasIndex("StudentId");

                    b.ToTable("HealthCheckupRecord");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AbnormalNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("BMI")
                        .HasColumnType("float");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dental")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Hearing")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Height")
                        .HasColumnType("float");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ParentNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccinationHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Vision")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Weight")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("StudentId");

                    b.ToTable("HealthProfile");

                    b.HasData(
                        new
                        {
                            Id = "83991635-875a-49c0-98e8-3d29f37fe849",
                            AbnormalNote = "None",
                            BMI = 20.5,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3671), new TimeSpan(0, 0, 0, 0, 0)),
                            Dental = "No cavities",
                            Hearing = "Normal",
                            Height = 0.0,
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            StudentId = "205fa6a1-bc72-41dd-950d-2c7a94f9fd13",
                            VaccinationHistory = "Fully using Rocket1h",
                            Vision = "20/20",
                            Weight = 0.0
                        },
                        new
                        {
                            Id = "63a313e4-9306-45d8-9650-49c67b5a8cb9",
                            AbnormalNote = "Monitor dental health",
                            BMI = 19.800000000000001,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3679), new TimeSpan(0, 0, 0, 0, 0)),
                            Dental = "Minor cavities",
                            Hearing = "Normal",
                            Height = 0.0,
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            StudentId = "86d5bbfb-e3fb-433b-99bf-68cbb479ad3f",
                            VaccinationHistory = "Fully using Rocket24/7",
                            Vision = "20/25",
                            Weight = 0.0
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalIncident", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("IncidentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("StudentId");

                    b.HasIndex("UserId");

                    b.ToTable("MedicalIncident");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalRequest", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dosage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Form")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Frequency")
                        .HasColumnType("int");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicationName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ParentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RemainingQuantity")
                        .HasColumnType("int");

                    b.Property<string>("Route")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TimeToAdminister")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("StudentId");

                    b.HasIndex("UserId");

                    b.ToTable("MedicalRequest");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalStock", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DetailInformation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Supplier")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MedicalStock");

                    b.HasData(
                        new
                        {
                            Id = "de3e49d9-6846-446c-b846-2c9d0c7e6e62",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(4121), new TimeSpan(0, 0, 0, 0, 0)),
                            DetailInformation = "A supplement for enhancing health and vitality",
                            ExpiryDate = new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Rocket1s",
                            Quantity = 100,
                            Status = 0,
                            Supplier = "PharmaCorp"
                        },
                        new
                        {
                            Id = "c6f2317a-abcb-40bf-95b6-a436cb925eb4",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(4132), new TimeSpan(0, 0, 0, 0, 0)),
                            DetailInformation = "A supplement for enhancing health and vitality",
                            ExpiryDate = new DateTime(2026, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Rocket1m",
                            Quantity = 50,
                            Status = 0,
                            Supplier = "MediSupply"
                        },
                        new
                        {
                            Id = "7d1262d4-d1b4-4e8e-abda-7cfb76e16e71",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(4138), new TimeSpan(0, 0, 0, 0, 0)),
                            DetailInformation = "A supplement for enhancing health and vitality",
                            ExpiryDate = new DateTime(2026, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Rocket1h",
                            Quantity = 50,
                            Status = 0,
                            Supplier = "HealthPlus"
                        },
                        new
                        {
                            Id = "774b09a7-e6c2-415b-ae01-9387ee1b6adc",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(4154), new TimeSpan(0, 0, 0, 0, 0)),
                            DetailInformation = "A supplement for enhancing health and vitality",
                            ExpiryDate = new DateTime(2026, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Rocket12h",
                            Quantity = 50,
                            Status = 0,
                            Supplier = "VitaCare"
                        },
                        new
                        {
                            Id = "de7ff374-9eea-402a-a883-a2417fdc7672",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(4158), new TimeSpan(0, 0, 0, 0, 0)),
                            DetailInformation = "A supplement for enhancing health and vitality",
                            ExpiryDate = new DateTime(2026, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Rocket-24/7",
                            Quantity = 50,
                            Status = 0,
                            Supplier = "WellnessPharma"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalUsage", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Dosage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicalIncidentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MedicalName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MedicalStockId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Supplier")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MedicalIncidentId");

                    b.HasIndex("MedicalStockId");

                    b.ToTable("MedicalUsage");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicationRequestAdministration", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AdministeredAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("AdministeredBy")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DoseGiven")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("MedicalRequestId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WasTaken")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("AdministeredBy");

                    b.HasIndex("MedicalRequestId");

                    b.ToTable("MedicationRequestAdministration");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Notification", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EventId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Notification");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Role", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Role");

                    b.HasData(
                        new
                        {
                            Id = "aa6e8bdd-e258-40f2-aa48-f1a57f3fc219",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1402), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Admin"
                        },
                        new
                        {
                            Id = "a494c983-072e-4dab-94d8-2cab26e29c3b",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1405), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1405), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Manager"
                        },
                        new
                        {
                            Id = "8c52409b-1e92-4e0e-823c-557059607698",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1409), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1410), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Nurse"
                        },
                        new
                        {
                            Id = "eb9346d1-7830-4475-94af-2e4673bb8484",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1423), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 37, 901, DateTimeKind.Unspecified).AddTicks(1423), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleName = "Parent"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.SchoolClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ClassName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClassRoom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SchoolClass");

                    b.HasData(
                        new
                        {
                            Id = "913409a4-230c-45dd-8d1b-6dcaecda2523",
                            ClassName = "Class 10A",
                            ClassRoom = "Room 101",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(2970), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Quantity = 30
                        },
                        new
                        {
                            Id = "3f738604-9ab0-470e-ba4a-41b0832e2794",
                            ClassName = "Class 10B",
                            ClassRoom = "Room 102",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(2975), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Quantity = 28
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Student", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ClassId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ParentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("StudentCode")
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("nvarchar(max)")
                        .HasComputedColumnSql("'STD' + CAST([StudentNumber] AS VARCHAR(10))");

                    b.Property<int>("StudentNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StudentNumber"));

                    b.HasKey("Id");

                    b.HasIndex("ClassId");

                    b.HasIndex("ParentId");

                    b.ToTable("Student");

                    b.HasData(
                        new
                        {
                            Id = "205fa6a1-bc72-41dd-950d-2c7a94f9fd13",
                            ClassId = "913409a4-230c-45dd-8d1b-6dcaecda2523",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3052), new TimeSpan(0, 0, 0, 0, 0)),
                            DateOfBirth = new DateTime(2010, 5, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FullName = "Nguyen Van A",
                            Gender = "Male",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            ParentId = "0f10cd22-135a-4b9b-8424-ac0c06e17bc2",
                            StudentNumber = 0
                        },
                        new
                        {
                            Id = "86d5bbfb-e3fb-433b-99bf-68cbb479ad3f",
                            ClassId = "3f738604-9ab0-470e-ba4a-41b0832e2794",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3496), new TimeSpan(0, 0, 0, 0, 0)),
                            DateOfBirth = new DateTime(2010, 8, 20, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            FullName = "Tran Thi B",
                            Gender = "Female",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            ParentId = "0f10cd22-135a-4b9b-8424-ac0c06e17bc2",
                            StudentNumber = 0
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("User");

                    b.HasData(
                        new
                        {
                            Id = "327bf02f-da2f-49f3-9146-9a3a1d7a7a23",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 23, DateTimeKind.Unspecified).AddTicks(8114), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "KICM vippro",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$rvD.BqKbjswNapU0ADG1EOBrQT3ogcTdG15Rw4S0Lyj9QDKsv6ugi",
                            Phone = "0987654321",
                            RoleId = "aa6e8bdd-e258-40f2-aa48-f1a57f3fc219"
                        },
                        new
                        {
                            Id = "6c21ab80-b663-4705-9bdc-29cba885ac5a",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 147, DateTimeKind.Unspecified).AddTicks(392), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "Jack97",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$G.fDcYSyj3M.2Lc4RPC/i.yQFNZAyITbFUuTGOImTQq59djpNuaJK",
                            Phone = "0912345678",
                            RoleId = "8c52409b-1e92-4e0e-823c-557059607698"
                        },
                        new
                        {
                            Id = "9d92a08d-66d1-4bb4-97b3-1be1b7eb3f60",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 265, DateTimeKind.Unspecified).AddTicks(534), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "FireFly",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$7kCUWtWk0BzSg0.sLHHa7O1udwIbYki.wykOt6b.1nnJbeHYDn31C",
                            Phone = "0987651234",
                            RoleId = "a494c983-072e-4dab-94d8-2cab26e29c3b"
                        },
                        new
                        {
                            Id = "0f10cd22-135a-4b9b-8424-ac0c06e17bc2",
                            CreatedBy = "SeedData",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(2166), new TimeSpan(0, 0, 0, 0, 0)),
                            Email = "<EMAIL>",
                            FullName = "KietBap",
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            Password = "$2a$11$5faVI/BNHRQgd9VSoGZoSe0.imeuEcx.gAQickYLaetXf03/v82li",
                            Phone = "0987051234",
                            RoleId = "eb9346d1-7830-4475-94af-2e4673bb8484"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaign", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("EXP")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTime>("MFG")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccineName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VaccineType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("VaccinationCampaign");

                    b.HasData(
                        new
                        {
                            Id = "ac9285a6-24e0-42b5-8f24-472d71e3a25f",
                            CreatedBy = "6c21ab80-b663-4705-9bdc-29cba885ac5a",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(3876), new TimeSpan(0, 0, 0, 0, 0)),
                            EXP = new DateTime(2025, 12, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EndDate = new DateTime(2024, 12, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            MFG = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "KT88",
                            StartDate = new DateTime(2024, 11, 15, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Status = 0,
                            UserId = "6c21ab80-b663-4705-9bdc-29cba885ac5a",
                            VaccineName = "Nha Cai Hang Dau So 1 Chau Au",
                            VaccineType = "Flu"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaignClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SchoolClassId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccinationCampaignId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("SchoolClassId");

                    b.HasIndex("VaccinationCampaignId");

                    b.ToTable("VaccinationCampaignClasses");

                    b.HasData(
                        new
                        {
                            Id = "bce033a4-be53-462b-9e0d-a5e417738b0c",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 14, 4, 10, 38, 386, DateTimeKind.Unspecified).AddTicks(4056), new TimeSpan(0, 0, 0, 0, 0)),
                            LastUpdatedTime = new DateTimeOffset(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            SchoolClassId = "3f738604-9ab0-470e-ba4a-41b0832e2794",
                            VaccinationCampaignId = "ac9285a6-24e0-42b5-8f24-472d71e3a25f"
                        });
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("DeletedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("LastUpdatedTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ResultNote")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("VaccinatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("VaccinationCampaignId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("StudentId");

                    b.HasIndex("VaccinationCampaignId");

                    b.ToTable("VaccinationRecord");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.ActivityConsent", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthActivity", "HealthActivity")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("HealthActivityId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.VaccinationCampaign", "VaccinationCampaign")
                        .WithMany("ActivityConsents")
                        .HasForeignKey("VaccinationCampaignId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("HealthActivity");

                    b.Navigation("Student");

                    b.Navigation("User");

                    b.Navigation("VaccinationCampaign");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Blog", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("Blogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.ConselingSchedule", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthCheckupRecord", "HealthCheckupRecord")
                        .WithMany("ConselingSchedules")
                        .HasForeignKey("HealthCheckupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "MedicalStaff")
                        .WithMany("StaffConselingSchedules")
                        .HasForeignKey("MedicalStaffId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "Parent")
                        .WithMany("ParentConselingSchedules")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("ConselingSchedules")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HealthCheckupRecord");

                    b.Navigation("MedicalStaff");

                    b.Navigation("Parent");

                    b.Navigation("Student");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivity", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("HealthActivities")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivityClass", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthActivity", "HealthActivity")
                        .WithMany("HealthActivityClasses")
                        .HasForeignKey("HealthActivityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.SchoolClass", "SchoolClass")
                        .WithMany()
                        .HasForeignKey("SchoolClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HealthActivity");

                    b.Navigation("SchoolClass");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthCheckupRecord", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.HealthActivity", "HealthActivity")
                        .WithMany("HealthCheckupRecords")
                        .HasForeignKey("HealthActivityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("HealthCheckupRecords")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("HealthActivity");

                    b.Navigation("Student");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthProfile", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("HealthProfiles")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Student");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalIncident", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("MedicalIncidents")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("MedicalIncidents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Student");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalRequest", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "Parent")
                        .WithMany()
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("MedicalRequests")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("MedicalRequests")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("Student");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalUsage", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.MedicalIncident", "MedicalIncident")
                        .WithMany("MedicalUsages")
                        .HasForeignKey("MedicalIncidentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.MedicalStock", "MedicalStock")
                        .WithMany("MedicalUsages")
                        .HasForeignKey("MedicalStockId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MedicalIncident");

                    b.Navigation("MedicalStock");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicationRequestAdministration", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "Administrator")
                        .WithMany()
                        .HasForeignKey("AdministeredBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("SMMS.Domain.Entity.MedicalRequest", "MedicalRequest")
                        .WithMany("MedicationRequestAdministrations")
                        .HasForeignKey("MedicalRequestId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Administrator");

                    b.Navigation("MedicalRequest");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Notification", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany("Notifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Student", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.SchoolClass", "SchoolClass")
                        .WithMany("Students")
                        .HasForeignKey("ClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.User", "Parent")
                        .WithMany("Students")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("SchoolClass");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.User", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaign", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaignClass", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.SchoolClass", "SchoolClass")
                        .WithMany()
                        .HasForeignKey("SchoolClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.VaccinationCampaign", "VaccinationCampaign")
                        .WithMany("VaccinationCampaignClasses")
                        .HasForeignKey("VaccinationCampaignId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SchoolClass");

                    b.Navigation("VaccinationCampaign");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationRecord", b =>
                {
                    b.HasOne("SMMS.Domain.Entity.Student", "Student")
                        .WithMany("VaccinationRecords")
                        .HasForeignKey("StudentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("SMMS.Domain.Entity.VaccinationCampaign", "VaccinationCampaign")
                        .WithMany("VaccinationRecords")
                        .HasForeignKey("VaccinationCampaignId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Student");

                    b.Navigation("VaccinationCampaign");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthActivity", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("HealthActivityClasses");

                    b.Navigation("HealthCheckupRecords");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.HealthCheckupRecord", b =>
                {
                    b.Navigation("ConselingSchedules");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalIncident", b =>
                {
                    b.Navigation("MedicalUsages");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalRequest", b =>
                {
                    b.Navigation("MedicationRequestAdministrations");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.MedicalStock", b =>
                {
                    b.Navigation("MedicalUsages");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Role", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.SchoolClass", b =>
                {
                    b.Navigation("Students");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.Student", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("ConselingSchedules");

                    b.Navigation("HealthCheckupRecords");

                    b.Navigation("HealthProfiles");

                    b.Navigation("MedicalIncidents");

                    b.Navigation("MedicalRequests");

                    b.Navigation("VaccinationRecords");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.User", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("Blogs");

                    b.Navigation("HealthActivities");

                    b.Navigation("MedicalIncidents");

                    b.Navigation("MedicalRequests");

                    b.Navigation("Notifications");

                    b.Navigation("ParentConselingSchedules");

                    b.Navigation("StaffConselingSchedules");

                    b.Navigation("Students");
                });

            modelBuilder.Entity("SMMS.Domain.Entity.VaccinationCampaign", b =>
                {
                    b.Navigation("ActivityConsents");

                    b.Navigation("VaccinationCampaignClasses");

                    b.Navigation("VaccinationRecords");
                });
#pragma warning restore 612, 618
        }
    }
}
