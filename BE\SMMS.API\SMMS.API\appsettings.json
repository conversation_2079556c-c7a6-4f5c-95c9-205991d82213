{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"Secret": "dGhpcyBpcyBhIHNlY3VyZSBrZXkgZm9yIEpXVA", "Issuer": "SchoolMedicalManagementSystem", "Audience": "SchoolMedicalUsers"}, "CloudinarySettings": {"CloudName": "dkepgafaf", "ApiKey": "351156844399788", "ApiSecret": "MAR-vurlqD7kq7nQhBXYNEawF2Q"}, "MailSettings": {"SmtpServer": "smtp.example.com", "Port": 587, "SenderName": "School Medical", "SenderEmail": "<EMAIL>", "Username": "smtp_user", "Password": "smtp_password"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": 587, "SenderName": "School Medical Management System", "SenderEmail": "<EMAIL>", "AppPassword": "gnmjhwhbyoovvigw"}, "ConnectionStrings": {"DefaultConnectionStringDB": "server=.; database=SchoolMedicalSystem;uid=sa;pwd=******; TrustServerCertificate=True", "Redis": "localhost:6379,abortConnect=false"}}